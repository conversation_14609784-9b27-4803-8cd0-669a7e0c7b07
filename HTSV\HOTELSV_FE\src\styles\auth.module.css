.authContainer {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  background: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)),
              url('/hinhanhtrangchu/anhbackgroundtrangchu.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.authCard {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 20px;
  padding: 2.5rem;
  width: 90%;
  max-width: 400px;
  box-shadow: 0 8px 32px rgba(31, 38, 135, 0.2);
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.authTitle {
  color: #fff;
  text-align: center;
  margin-bottom: 1.5rem;
  font-size: 1.75rem;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.authForm {
  display: flex;
  flex-direction: column;
}

.formGroup {
  margin-bottom: 1.2rem;
  position: relative;
  width: 100%;
}

.formGroup label {
  display: block;
  margin-bottom: 0.5rem;
  color: rgba(255, 255, 255, 0.9);
  font-size: 0.95rem;
  font-weight: 500;
  letter-spacing: 0.3px;
}

.formGroup input {
  width: 100%;
  padding: 0.8rem 1rem;
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
  font-size: 0.95rem;
  letter-spacing: 0.3px;
  transition: all 0.3s ease;
  box-sizing: border-box;
  max-width: 100%;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.formGroup input.error {
  border-color: #ff4d4f;
}

.formGroup input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.formGroup input:focus {
  outline: none;
  border-color: rgba(255, 255, 255, 0.5);
  background: rgba(255, 255, 255, 0.15);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.authButton {
  width: 100%;
  max-width: 100%;
  padding: 0.8rem;
  margin-top: 1.5rem;
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.2);
  color: #fff;
  font-size: 0.95rem;
  font-weight: 500;
  letter-spacing: 0.5px;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(8px);
  box-sizing: border-box;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.authButton:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.authSwitch {
  margin-top: 1.5rem;
  text-align: center;
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
}

.authLink {
  color: #fff;
  text-decoration: none;
  font-weight: 500;
  transition: opacity 0.3s ease;
}

.authLink:hover {
  opacity: 0.8;
}

.errorText {
  color: #fca5a5;
  font-size: 0.85rem;
  margin-top: 0.25rem;
}