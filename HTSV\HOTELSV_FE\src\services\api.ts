import axios from 'axios';

// Tạo instance axios với cấu hình cơ bản
const api = axios.create({
  baseURL: 'http://localhost:5001/api', // Đổi port thành 5001 theo .env
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: 10000, // Thêm timeout
});

// Thêm interceptor để xử lý token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    console.error('Request Error:', error);
    return Promise.reject(error);
  }
);

// Thêm interceptor để xử lý response
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (!error.response) {
      console.error('Network Error:', error);
      throw new Error('Network error - please check if the server is running');
    }
    throw error;
  }
);

export const roomApi = {
  getAllRooms: () => api.get('/Rooms/allRoom'),
  getAvailableRooms: () => api.get('/Rooms/available'),
  getRoomById: (id: number) => api.get(`/Rooms/${id}`),
};

export default api;
