using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using HOTELSV_BE.Models;
using System.Data.SqlClient;
using Dapper;
using HOTELSV_BE.Attributes;

namespace HOTELSV_BE.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class RolePermissionController : ControllerBase
    {
        private readonly string _connectionString;

        public RolePermissionController(IConfiguration configuration)
        {
            _connectionString = configuration.GetConnectionString("DefaultConnection");
        }

        [HttpPost("Add")]
        [RequirePermission("create_rolepermission")]
        public async Task<ActionResult<AddRolePermissionResponse>> AddRolePermission([FromBody] AddRolePermissionRequest request)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    var parameters = new
                    {
                        RoleId = request.RoleId,
                        PermissionId = request.PermissionId
                    };

                    var id = await connection.QueryFirstOrDefaultAsync<int>(
                        "sp_AddRolePermission",
                        parameters,
                        commandType: System.Data.CommandType.StoredProcedure
                    );

                    return Ok(new AddRolePermissionResponse
                    {
                        Success = true,
                        Message = "Role permission mapping added successfully",
                        RolePermissionId = id
                    });
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new AddRolePermissionResponse
                {
                    Success = false,
                    Message = $"Error adding role permission: {ex.Message}"
                });
            }
        }

        [HttpDelete("{id}")]
        [RequirePermission("cancel_rolepermission")]
        public async Task<ActionResult<DeleteRolePermissionResponse>> DeleteRolePermission(int id)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    await connection.ExecuteAsync(
                        "sp_DeleteRolePermission",
                        new { RolePermissionId = id },
                        commandType: System.Data.CommandType.StoredProcedure
                    );

                    return Ok(new DeleteRolePermissionResponse
                    {
                        Success = true,
                        Message = "Role permission mapping deleted successfully"
                    });
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new DeleteRolePermissionResponse
                {
                    Success = false,
                    Message = $"Error deleting role permission: {ex.Message}"
                });
            }
        }

        [HttpPut("Update")]
        [RequirePermission("edit_rolepermission")]
        public async Task<ActionResult<UpdateRolePermissionResponse>> UpdateRolePermission([FromBody] UpdateRolePermissionRequest request)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    var parameters = new
                    {
                        RolePermissionId = request.RolePermissionId,
                        RoleId = request.RoleId,
                        PermissionId = request.PermissionId
                    };

                    var updated = await connection.QueryFirstOrDefaultAsync<RolePermission>(
                        "sp_UpdateRolePermission",
                        parameters,
                        commandType: System.Data.CommandType.StoredProcedure
                    );

                    if (updated == null)
                    {
                        return NotFound(new UpdateRolePermissionResponse
                        {
                            Success = false,
                            Message = "Role permission mapping not found"
                        });
                    }

                    return Ok(new UpdateRolePermissionResponse
                    {
                        Success = true,
                        Message = "Role permission mapping updated successfully",
                        RolePermission = updated
                    });
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new UpdateRolePermissionResponse
                {
                    Success = false,
                    Message = $"Error updating role permission: {ex.Message}"
                });
            }
        }

        [HttpGet]
        [RequirePermission("view_rolepermissions")]
        public async Task<ActionResult<GetAllRolePermissionsResponse>> GetAllRolePermissions()
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    var rolePermissions = await connection.QueryAsync<RolePermission>(
                        "sp_GetAllRolePermissions",
                        commandType: System.Data.CommandType.StoredProcedure
                    );

                    return Ok(new GetAllRolePermissionsResponse
                    {
                        Success = true,
                        Message = "Role permissions retrieved successfully",
                        RolePermissions = rolePermissions.ToList()
                    });
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new GetAllRolePermissionsResponse
                {
                    Success = false,
                    Message = $"Error retrieving role permissions: {ex.Message}"
                });
            }
        }

        [HttpGet("{id}")]
        [RequirePermission("view_rolepermissions")]
        public async Task<ActionResult<GetRolePermissionResponse>> GetRolePermissionById(int id)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    var rolePermission = await connection.QueryFirstOrDefaultAsync<RolePermission>(
                        "sp_GetRolePermissionById",
                        new { RolePermissionId = id },
                        commandType: System.Data.CommandType.StoredProcedure
                    );

                    if (rolePermission == null)
                    {
                        return NotFound(new GetRolePermissionResponse
                        {
                            Success = false,
                            Message = "Role permission mapping not found"
                        });
                    }

                    return Ok(new GetRolePermissionResponse
                    {
                        Success = true,
                        Message = "Role permission mapping retrieved successfully",
                        RolePermission = rolePermission
                    });
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new GetRolePermissionResponse
                {
                    Success = false,
                    Message = $"Error retrieving role permission: {ex.Message}"
                });
            }
        }
    }
}
