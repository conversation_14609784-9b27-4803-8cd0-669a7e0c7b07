import React, { useState } from 'react';
import styles from '../styles/bookingModal.module.css';

interface BookingModalProps {
  isOpen: boolean;
  onClose: () => void;
  room: {
    name: string;
    price: string;
    image: string;
  };
}

const BookingModal: React.FC<BookingModalProps> = ({ isOpen, onClose, room }) => {
  if (!isOpen) return null;

  const [bookingData, setBookingData] = useState({
    fullName: '',
    phoneNumber: '',
    checkIn: '',
    checkOut: '',
    guests: 1,
    specialRequests: ''
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setBookingData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Booking data:', bookingData);
    onClose();
  };

  return (
    <div className={styles.modalOverlay} onClick={onClose}>
      <div className={styles.modal} onClick={e => e.stopPropagation()}>
        <button className={styles.closeButton} onClick={onClose}>&times;</button>
        
        <div className={styles.modalContent}>
          <div className={styles.roomInfo}>
            <img src={room.image} alt={room.name} className={styles.roomImage} />
            <div className={styles.roomDetails}>
              <h3>{room.name}</h3>
              <p className={styles.price}>{room.price}</p>
            </div>
          </div>

          <form onSubmit={handleSubmit} className={styles.bookingForm}>
            <div className={styles.formGroup}>
              <label>Họ và tên</label>
              <input
                type="text"
                name="fullName"
                value={bookingData.fullName}
                onChange={handleChange}
                placeholder="Nhập họ và tên"
                required
              />
            </div>

            <div className={styles.formGroup}>
              <label>Số điện thoại</label>
              <input
                type="tel"
                name="phoneNumber"
                value={bookingData.phoneNumber}
                onChange={handleChange}
                placeholder="Nhập số điện thoại"
                required
              />
            </div>

            <div className={styles.formGroup}>
              <label>Ngày nhận phòng</label>
              <input
                type="date"
                name="checkIn"
                value={bookingData.checkIn}
                onChange={handleChange}
                required
              />
            </div>

            <div className={styles.formGroup}>
              <label>Ngày trả phòng</label>
              <input
                type="date"
                name="checkOut"
                value={bookingData.checkOut}
                onChange={handleChange}
                required
              />
            </div>

            <div className={styles.formGroup}>
              <label>Số lượng khách</label>
              <input
                type="number"
                name="guests"
                min="1"
                max="4"
                value={bookingData.guests}
                onChange={handleChange}
                required
              />
            </div>

            <div className={`${styles.formGroup} ${styles['full-width']}`}>
              <label>Yêu cầu đặc biệt</label>
              <textarea
                name="specialRequests"
                value={bookingData.specialRequests}
                onChange={handleChange}
                rows={3}
              />
            </div>

            <button type="submit" className={styles.submitButton}>
              Xác nhận đặt phòng
            </button>
          </form>
        </div>
      </div>
    </div>
  );
};

export default BookingModal;