namespace HOTELSV_BE.Models
{
    public class RoomModels
    {
        public int RoomId { get; set; }
        public string RoomNumber { get; set; } = string.Empty;
        public int RoomTypeId { get; set; }
        public string RoomType { get; set; } = string.Empty;
        public int Floor { get; set; }
        public string Status { get; set; } = string.Empty;
        public string CleaningStatus { get; set; } = string.Empty;
        public decimal BasePrice { get; set; }
        public int Capacity { get; set; }
        public string BedType { get; set; } = string.Empty;
        public string Amenities { get; set; } = string.Empty;
    }

    public class AddRoomModels
    {
        public string RoomNumber { get; set; } = string.Empty;
        public int RoomTypeId { get; set; }
        public int Floor { get; set; }
        public string CleaningStatus { get; set; } = string.Empty;
    }
    public class UpdateRoomModels
    {
        public string RoomNumber { get; set; } = string.Empty;
        public int RoomTypeId { get; set; }
        public int Floor { get; set; }
        public string Status { get; set; } = string.Empty;
        public string CleaningStatus { get; set; } = string.Empty;
    }
}