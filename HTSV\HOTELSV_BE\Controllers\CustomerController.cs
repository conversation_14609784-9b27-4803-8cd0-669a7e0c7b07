using Microsoft.AspNetCore.Mvc;
using System.Data.SqlClient;
using System.Data;
using HOTELSV_BE.Models;
using Microsoft.AspNetCore.Authorization;
using System.Security.Claims;
using Dapper;
using HOTELSV_BE.Attributes;

namespace HOTELSV_BE.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class CustomerController : ControllerBase
    {
        private readonly IConfiguration _configuration;
        private readonly string _connectionString;

        public CustomerController(IConfiguration configuration)
        {
            _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
            _connectionString = _configuration.GetConnectionString("DefaultConnection") ?? throw new InvalidOperationException("Connection string 'DefaultConnection' not found.");
        }

        [HttpPost("Add")]
        [RequirePermission("create_customer")]
        public async Task<IActionResult> AddCustomer([FromBody] AddCustomerRequest request)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    await connection.OpenAsync();

                    var parameters = new DynamicParameters();
                    parameters.Add("@Username", request.Username);
                    parameters.Add("@PasswordHash", request.PasswordHash);
                    parameters.Add("@Email", request.Email);
                    parameters.Add("@FirstName", request.FirstName);
                    parameters.Add("@LastName", request.LastName);
                    parameters.Add("@Phone", request.Phone);
                    parameters.Add("@Address", request.Address);
                    parameters.Add("@City", request.City);
                    parameters.Add("@Country", request.Country);
                    parameters.Add("@PostalCode", request.PostalCode);
                    parameters.Add("@IDNumber", request.IDNumber);
                    parameters.Add("@IDType", request.IDType);
                    parameters.Add("@Nationality", request.Nationality);

                    var result = await connection.QueryFirstOrDefaultAsync<AddCustomerResponse>(
                        "sp_AddCustomer",
                        parameters,
                        commandType: CommandType.StoredProcedure
                    );

                    if (result != null && result.IsSuccess)
                    {
                        return Ok(new {
                            success = true,
                            message = "Customer added successfully",
                            data = result
                        });
                    }
                    else
                    {
                        return BadRequest(new {
                            success = false,
                            message = result?.ErrorMessage ?? "Failed to add customer",
                            //data = (object)null
                        });
                    }
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new {
                    success = false,
                    message = "An error occurred while processing your request",
                    data = ex.Message
                });
            }
        }
        [HttpDelete("Delete/{id}")]
        [RequirePermission("cancel_customer")]
        public async Task<IActionResult> DeleteCustomer(int id)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    await connection.OpenAsync();

                    var parameters = new DynamicParameters();
                    parameters.Add("@CustomerId", id);

                    var result = await connection.QueryFirstOrDefaultAsync<DeleteCustomerResponse>(
                        "sp_DeleteCustomer",
                        parameters,
                        commandType: CommandType.StoredProcedure
                    );

                    if (result != null && result.IsSuccess)
                    {
                        return Ok(new {
                            success = true,
                            message = result.Message ?? "Customer deleted successfully",
                            data = result
                        });
                    }
                    else
                    {
                        return BadRequest(new {
                            success = false,
                            message = result?.ErrorMessage ?? "Failed to delete customer"
                        });
                    }
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new {
                    success = false,
                    message = "An error occurred while processing your request",
                    data = ex.Message
                });
            }
        }

        [HttpPut("Update/{id}")]
        [RequirePermission("edit_customers")]
        public async Task<IActionResult> UpdateCustomer(int id, [FromBody] UpdateCustomerRequest request)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    await connection.OpenAsync();

                    var parameters = new DynamicParameters();
                    parameters.Add("@CustomerId", id);
                    parameters.Add("@Address", request.Address);
                    parameters.Add("@City", request.City);
                    parameters.Add("@Country", request.Country);
                    parameters.Add("@PostalCode", request.PostalCode);
                    parameters.Add("@IDNumber", request.IDNumber);
                    parameters.Add("@IDType", request.IDType);
                    parameters.Add("@Nationality", request.Nationality);

                    var result = await connection.QueryFirstOrDefaultAsync<UpdateCustomerResponse>(
                        "sp_UpdateCustomer",
                        parameters,
                        commandType: CommandType.StoredProcedure
                    );

                    if (result != null && result.IsSuccess)
                    {
                        return Ok(new {
                            success = true,
                            message = "Customer updated successfully",
                            data = result
                        });
                    }
                    else
                    {
                        return BadRequest(new {
                            success = false,
                            message = result?.ErrorMessage ?? "Failed to update customer"
                        });
                    }
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new {
                    success = false,
                    message = "An error occurred while processing your request",
                    data = ex.Message
                });
            }
        }

        [HttpGet("GetAll")]
        [Authorize]
        [RequirePermission("view_customers")]
        public async Task<IActionResult> GetAllCustomers()
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    await connection.OpenAsync();

                    var customers = await connection.QueryAsync<GetAllCustomersResponse>(
                        "GetAllCustomers",
                        commandType: CommandType.StoredProcedure
                    );

                    return Ok(new {
                        success = true,
                        message = "Retrieved customers successfully",
                        data = customers
                    });
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new {
                    success = false,
                    message = "An error occurred while retrieving customers",
                    data = ex.Message
                });
            }
        }

        [HttpGet("GetById/{id}")]
        [Authorize]
        [RequirePermission("view_customers")]
        public async Task<IActionResult> GetCustomerById(int id)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    await connection.OpenAsync();

                    var parameters = new DynamicParameters();
                    parameters.Add("@CustomerId", id);

                    var customer = await connection.QueryFirstOrDefaultAsync<GetAllCustomersResponse>(
                        "GetCustomerById",
                        parameters,
                        commandType: CommandType.StoredProcedure
                    );

                    if (customer == null)
                    {
                        return NotFound(new {
                            success = false,
                            message = $"Customer not found with ID: {id}"
                        });
                    }

                    return Ok(new {
                        success = true,
                        message = "Retrieved customer successfully",
                        data = customer
                    });
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new {
                    success = false,
                    message = "An error occurred while retrieving customer",
                    data = ex.Message
                });
            }
        }
    }
}
