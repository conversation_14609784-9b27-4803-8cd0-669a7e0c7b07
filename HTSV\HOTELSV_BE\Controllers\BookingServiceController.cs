using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using System.Data.SqlClient;
using Dapper;
using System.Data;
using HOTELSV_BE.Models;
using HOTELSV_BE.Attributes;

namespace HOTELSV_BE.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class BookingServiceController : ControllerBase
    {
        private readonly string _connectionString;
        private readonly ILogger<BookingServiceController> _logger;

        public BookingServiceController(IConfiguration configuration, ILogger<BookingServiceController> logger)
        {
            _connectionString = configuration.GetConnectionString("DefaultConnection")
                ?? throw new ArgumentNullException(nameof(configuration));
            _logger = logger;
        }

        [HttpPost("Add")]
        [RequirePermission("create_bookingservice")]
        public async Task<IActionResult> AddBookingService([FromBody] AddBookingServiceRequest request)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    var parameters = new DynamicParameters();
                    parameters.Add("@BookingId", request.BookingId);
                    parameters.Add("@ServiceId", request.ServiceId);
                    parameters.Add("@Quantity", request.Quantity);
                    parameters.Add("@ServiceDate", request.ServiceDate ?? DateTime.Now);
                    parameters.Add("@Notes", request.Notes);
                    parameters.Add("@RequestedBy", request.RequestedBy);

                    var result = await connection.QueryFirstOrDefaultAsync<dynamic>(
                        "sp_AddBookingService",
                        parameters,
                        commandType: CommandType.StoredProcedure
                    );

                    if (result?.ErrorMessage != null)
                    {
                        return BadRequest(new
                        {
                            success = false,
                            message = result.ErrorMessage
                        });
                    }

                    var bookingService = new BookingServiceResponse
                    {
                        BookingServiceId = result.BookingServiceId,
                        BookingId = result.BookingId,
                        ServiceId = result.ServiceId,
                        Quantity = result.Quantity,
                        Price = result.Price,
                        ServiceDate = result.ServiceDate,
                        Status = result.Status,
                        Notes = result.Notes,
                        RequestedBy = result.RequestedBy,
                        ServedBy = result.ServedBy
                    };

                    return Ok(new
                    {
                        success = true,
                        message = "Thêm dịch vụ thành công",
                        data = bookingService
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Lỗi khi thêm dịch vụ cho đặt phòng");
                return StatusCode(500, new
                {
                    success = false,
                    message = "Lỗi server, vui lòng thử lại sau"
                });
            }
        }
        [HttpPut("Update/{id}")]
        [RequirePermission("edit_bookingservice")]
        public async Task<IActionResult> UpdateBookingService(int id, [FromBody] UpdateBookingServiceRequest request)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    var parameters = new DynamicParameters();
                    parameters.Add("@BookingServiceId", id);
                    parameters.Add("@BookingId", request.BookingId);
                    parameters.Add("@ServiceId", request.ServiceId);
                    parameters.Add("@Quantity", request.Quantity);
                    parameters.Add("@Price", request.Price);
                    parameters.Add("@ServiceDate", request.ServiceDate);
                    parameters.Add("@Status", request.Status);
                    parameters.Add("@Notes", request.Notes);
                    parameters.Add("@RequestedBy", request.RequestedBy);
                    parameters.Add("@ServedBy", request.ServedBy);

                    var result = await connection.QueryFirstOrDefaultAsync<dynamic>(
                        "sp_UpdateBookingService",
                        parameters,
                        commandType: CommandType.StoredProcedure
                    );

                    if (result?.ErrorMessage != null)
                    {
                        return BadRequest(new
                        {
                            success = false,
                            message = result.ErrorMessage
                        });
                    }

                    var bookingService = new UpdateBookingServiceResponse
                    {
                        BookingServiceId = result.BookingServiceId,
                        BookingId = result.BookingId,
                        ServiceId = result.ServiceId,
                        Quantity = result.Quantity,
                        Price = result.Price,
                        ServiceDate = result.ServiceDate,
                        Status = result.Status,
                        Notes = result.Notes,
                        RequestedBy = result.RequestedBy,
                        ServedBy = result.ServedBy,
                        ServiceName = result.ServiceName,
                        ServiceDescription = result.ServiceDescription,
                        RequestedByName = result.RequestedByName,
                        ServedByName = result.ServedByName
                    };

                    return Ok(new
                    {
                        success = true,
                        message = "Cập nhật dịch vụ thành công",
                        data = bookingService
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Lỗi khi cập nhật dịch vụ đặt phòng");
                return StatusCode(500, new
                {
                    success = false,
                    message = "Lỗi server, vui lòng thử lại sau"
                });
            }
        }
        [HttpGet("GetAll")]
        [RequirePermission("view_bookingservices")]
        public async Task<IActionResult> GetAllBookingServices()
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    var bookingServices = await connection.QueryAsync<BookingServiceListItem>(
                        "GetAllBookingServices",
                        commandType: CommandType.StoredProcedure
                    );

                    return Ok(new
                    {
                        success = true,
                        message = "Lấy danh sách dịch vụ thành công",
                        data = bookingServices
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Lỗi khi lấy danh sách dịch vụ đặt phòng");
                return StatusCode(500, new
                {
                    success = false,
                    message = "Lỗi server, vui lòng thử lại sau"
                });
            }
        }
        [HttpGet("{id}")]
        [RequirePermission("view_bookingservices")]
        public async Task<IActionResult> GetBookingServiceById(int id)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    var parameters = new DynamicParameters();
                    parameters.Add("@BookingServiceId", id);

                    var bookingService = await connection.QueryFirstOrDefaultAsync<BookingServiceDetail>(
                        "GetBookingServicesByID",
                        parameters,
                        commandType: CommandType.StoredProcedure
                    );

                    if (bookingService == null)
                    {
                        return NotFound(new
                        {
                            success = false,
                            message = $"Không tìm thấy dịch vụ đặt phòng với ID: {id}"
                        });
                    }

                    return Ok(new
                    {
                        success = true,
                        message = "Lấy thông tin dịch vụ thành công",
                        data = bookingService
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Lỗi khi lấy thông tin dịch vụ đặt phòng");
                return StatusCode(500, new
                {
                    success = false,
                    message = "Lỗi server, vui lòng thử lại sau"
                });
            }
        }

        [HttpDelete("{id}")]
        [RequirePermission("cancel_bookingservice")]
        public async Task<IActionResult> DeleteBookingService(int id)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    var parameters = new DynamicParameters();
                    parameters.Add("@BookingServiceId", id);

                    var result = await connection.QueryFirstOrDefaultAsync<dynamic>(
                        "sp_DeleteBookingService",
                        parameters,
                        commandType: CommandType.StoredProcedure
                    );

                    if (result?.ErrorMessage != null)
                    {
                        return BadRequest(new
                        {
                            success = false,
                            message = result.ErrorMessage
                        });
                    }

                    return Ok(new
                    {
                        success = true,
                        message = "Xóa dịch vụ đặt phòng thành công"
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Lỗi khi xóa dịch vụ đặt phòng");
                return StatusCode(500, new
                {
                    success = false,
                    message = "Lỗi server, vui lòng thử lại sau"
                });
            }
        }
    }
}