using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using System.Data.SqlClient;
using Dapper;
using System.Data;
using HOTELSV_BE.Models;
using HOTELSV_BE.Attributes;

namespace HOTELSV_BE.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class InvoiceController : ControllerBase
    {
        private readonly string _connectionString;

        public InvoiceController(IConfiguration configuration)
        {
            _connectionString = configuration.GetConnectionString("DefaultConnection") ??
                throw new ArgumentNullException(nameof(configuration), "DefaultConnection string is not configured");
        }

        [HttpGet("GetAll")]
        [RequirePermission("view_invoices")]
        public async Task<IActionResult> GetAllInvoices()
        {
            using (var connection = new SqlConnection(_connectionString))
            {
                var invoices = await connection.QueryAsync<InvoiceModels>(
                    "SELECT * FROM Invoices"
                );
                return Ok(invoices);
            }
        }

        [HttpGet("GetById/{id}")]
        [RequirePermission("view_invoices")]
        public async Task<IActionResult> GetInvoiceById(int id)
        {
            using (var connection = new SqlConnection(_connectionString))
            {
                var invoice = await connection.QueryFirstOrDefaultAsync<InvoiceModels>(
                    "sp_GetInvoiceDetails",
                    new { InvoiceId = id },
                    commandType: CommandType.StoredProcedure
                );

                if (invoice == null)
                    return NotFound(new { message = "Không tìm thấy hóa đơn với ID này." });

                return Ok(invoice);
            }
        }

        [HttpPost("Add")]
        [RequirePermission("create_invoice")]
        public async Task<IActionResult> AddInvoice(AddInvoiceModels invoice)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    var parameters = new DynamicParameters();
                    parameters.Add("@BookingId", invoice.BookingId);
                    parameters.Add("@InvoiceNumber", invoice.InvoiceNumber);
                    parameters.Add("@DueDate", invoice.DueDate);
                    parameters.Add("@TotalAmount", invoice.TotalAmount);
                    parameters.Add("@TaxAmount", invoice.TaxAmount);
                    parameters.Add("@DiscountAmount", invoice.DiscountAmount);
                    parameters.Add("@Notes", invoice.Notes);
                    parameters.Add("@IssuedBy", invoice.IssuedBy);

                    var result = await connection.QueryFirstOrDefaultAsync<InvoiceModels>(
                        "sp_AddInvoice",
                        parameters,
                        commandType: CommandType.StoredProcedure
                    );

                    return Ok(new {
                        success = true,
                        message = "Thêm hóa đơn thành công",
                        data = result
                    });
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new {
                    success = false,
                    message = "Lỗi khi thêm hóa đơn",
                    error = ex.Message
                });
            }
        }

        [HttpPut("Update/{id}")]
        [RequirePermission("edit_invoice")]
        public async Task<IActionResult> UpdateInvoice(int id, UpdateInvoiceModels invoice)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    var parameters = new DynamicParameters();
                    parameters.Add("@InvoiceId", id);
                    parameters.Add("@BookingId", invoice.BookingId);
                    parameters.Add("@InvoiceNumber", invoice.InvoiceNumber);
                    parameters.Add("@DueDate", invoice.DueDate);
                    parameters.Add("@TotalAmount", invoice.TotalAmount);
                    parameters.Add("@TaxAmount", invoice.TaxAmount);
                    parameters.Add("@DiscountAmount", invoice.DiscountAmount);
                    parameters.Add("@Status", invoice.Status);
                    parameters.Add("@Notes", invoice.Notes);
                    parameters.Add("@IssuedBy", invoice.IssuedBy);

                    var result = await connection.ExecuteAsync(
                        "sp_UpdateInvoice",
                        parameters,
                        commandType: CommandType.StoredProcedure
                    );

                    if (result == 0)
                    {
                        return NotFound(new {
                            success = false,
                            message = $"Không tìm thấy hóa đơn với ID: {id}"
                        });
                    }

                    return Ok(new {
                        success = true,
                        message = "Cập nhật hóa đơn thành công"
                    });
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new {
                    success = false,
                    message = "Lỗi khi cập nhật hóa đơn",
                    error = ex.Message
                });
            }
        }

        [HttpDelete("Delete/{id}")]
        [RequirePermission("cancel_invoice")]
        public async Task<IActionResult> DeleteInvoice(int id)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    // First delete related invoice items
                    await connection.ExecuteAsync(
                        "DELETE FROM InvoiceItems WHERE InvoiceId = @Id",
                        new { Id = id }
                    );

                    // Then delete the invoice
                    var result = await connection.ExecuteAsync(
                        "DELETE FROM Invoices WHERE InvoiceId = @Id",
                        new { Id = id }
                    );

                    if (result == 0)
                    {
                        return NotFound(new {
                            success = false,
                            message = $"Không tìm thấy hóa đơn với ID: {id}"
                        });
                    }

                    return Ok(new {
                        success = true,
                        message = "Xóa hóa đơn thành công"
                    });
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new {
                    success = false,
                    message = "Lỗi khi xóa hóa đơn",
                    error = ex.Message
                });
            }
        }

        // Invoice Items methods
        [HttpGet("Items/{invoiceId}")]
        [RequirePermission("view_invoiceitems")]
        public async Task<IActionResult> GetInvoiceItems(int invoiceId)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    var items = await connection.QueryAsync<dynamic>(
                        "SELECT * FROM InvoiceItems WHERE InvoiceId = @InvoiceId",
                        new { InvoiceId = invoiceId }
                    );
                    return Ok(new {
                        success = true,
                        data = items
                    });
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new {
                    success = false,
                    message = "Lỗi khi lấy danh sách mục hóa đơn",
                    error = ex.Message
                });
            }
        }

        [HttpPost("Items")]
        [RequirePermission("create_invoiceitem")]
        public async Task<IActionResult> AddInvoiceItem(dynamic invoiceItem)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    var parameters = new DynamicParameters();
                    parameters.Add("@InvoiceId", invoiceItem.InvoiceId);
                    parameters.Add("@Description", invoiceItem.Description);
                    parameters.Add("@Quantity", invoiceItem.Quantity);
                    parameters.Add("@UnitPrice", invoiceItem.UnitPrice);
                    parameters.Add("@TotalPrice", invoiceItem.TotalPrice);

                    var result = await connection.QueryFirstOrDefaultAsync<dynamic>(
                        "sp_AddInvoiceItem",
                        parameters,
                        commandType: CommandType.StoredProcedure
                    );

                    return Ok(new {
                        success = true,
                        message = "Thêm mục hóa đơn thành công",
                        data = result
                    });
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new {
                    success = false,
                    message = "Lỗi khi thêm mục hóa đơn",
                    error = ex.Message
                });
            }
        }

        [HttpPut("Items/{id}")]
        [RequirePermission("edit_invoiceitem")]
        public async Task<IActionResult> UpdateInvoiceItem(int id, dynamic invoiceItem)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    var parameters = new DynamicParameters();
                    parameters.Add("@InvoiceItemId", id);
                    parameters.Add("@Description", invoiceItem.Description);
                    parameters.Add("@Quantity", invoiceItem.Quantity);
                    parameters.Add("@UnitPrice", invoiceItem.UnitPrice);
                    parameters.Add("@TotalPrice", invoiceItem.TotalPrice);

                    var result = await connection.ExecuteAsync(
                        "sp_UpdateInvoiceItem",
                        parameters,
                        commandType: CommandType.StoredProcedure
                    );

                    if (result == 0)
                    {
                        return NotFound(new {
                            success = false,
                            message = $"Không tìm thấy mục hóa đơn với ID: {id}"
                        });
                    }

                    return Ok(new {
                        success = true,
                        message = "Cập nhật mục hóa đơn thành công"
                    });
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new {
                    success = false,
                    message = "Lỗi khi cập nhật mục hóa đơn",
                    error = ex.Message
                });
            }
        }

        [HttpDelete("Items/{id}")]
        [RequirePermission("cancel_invoiceitem")]
        public async Task<IActionResult> DeleteInvoiceItem(int id)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    var result = await connection.ExecuteAsync(
                        "DELETE FROM InvoiceItems WHERE InvoiceItemId = @Id",
                        new { Id = id }
                    );

                    if (result == 0)
                    {
                        return NotFound(new {
                            success = false,
                            message = $"Không tìm thấy mục hóa đơn với ID: {id}"
                        });
                    }

                    return Ok(new {
                        success = true,
                        message = "Xóa mục hóa đơn thành công"
                    });
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new {
                    success = false,
                    message = "Lỗi khi xóa mục hóa đơn",
                    error = ex.Message
                });
            }
        }
    }
}