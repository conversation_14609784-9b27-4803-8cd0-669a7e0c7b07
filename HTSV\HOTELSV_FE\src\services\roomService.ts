import axiosInstance from './axiosInstance';

const roomService = {
  async getAllRooms() {
    try {
      const response = await axiosInstance.get('/Rooms/allRoom');
      return response.data;
    } catch (error: any) {
      if (error.response?.status === 401) {
        throw new Error('Vui lòng đăng nhập để xem danh sách phòng');
      }
      throw new Error('Không thể tải danh sách phòng');
    }
  },

  async getAvailableRooms() {
    try {
      const response = await axiosInstance.get('/Rooms/available');
      return response.data;
    } catch (error: any) {
      throw new Error('Không thể tải danh sách phòng trống');
    }
  },

  async getRoomById(id: number) {
    try {
      const response = await axiosInstance.get(`/Rooms/${id}`);
      return response.data;
    } catch (error: any) {
      throw new Error('<PERSON>hông thể tải thông tin phòng');
    }
  }
};

export { roomService };
