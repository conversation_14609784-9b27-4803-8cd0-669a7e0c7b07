import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { roomService } from '../services/roomService';
import { Room } from '../types/room';
import styles from '../styles/rooms.module.css';
import BookingModal from './BookingModal';
import { toast } from 'react-toastify';
import Image from 'next/image';

const RoomList = () => {
  const router = useRouter();
  const [rooms, setRooms] = useState<Room[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [selectedRoom, setSelectedRoom] = useState<Room | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [filter, setFilter] = useState('all'); // all, available, booked

  useEffect(() => {
    const fetchRooms = async () => {
      try {
        setLoading(true);
        const data = await roomService.getAllRooms();
        setRooms(data);
        setError('');
      } catch (error: any) {
        setError(error.message);
        if (error.message.includes('đăng nhập')) {
          router.push('/auth/login');
        }
      } finally {
        setLoading(false);
      }
    };

    fetchRooms();
  }, [router]);

  const handleBooking = (room: Room) => {
    setSelectedRoom(room);
    setIsModalOpen(true);
  };

  const filteredRooms = rooms.filter(room => {
    if (filter === 'available') return room.status === 'Available';
    if (filter === 'booked') return room.status === 'Booked';
    return true;
  });

  if (loading) return <div className={styles.loading}>Loading rooms...</div>;
  if (error) return <div className={styles.error}>{error}</div>;

  return (
    <div className={styles.roomsContainer}>
      <div className={styles.filterTabs}>
        <button 
          className={`${styles.filterTab} ${filter === 'all' ? styles.active : ''}`}
          onClick={() => setFilter('all')}
        >
          Tất cả phòng
        </button>
        <button 
          className={`${styles.filterTab} ${filter === 'available' ? styles.active : ''}`}
          onClick={() => setFilter('available')}
        >
          Phòng có sẵn
        </button>
        <button 
          className={`${styles.filterTab} ${filter === 'booked' ? styles.active : ''}`}
          onClick={() => setFilter('booked')}
        >
          Phòng đã đặt
        </button>
      </div>

      <div className={styles.roomsGrid}>
        {filteredRooms.map((room) => (
          <div key={room.roomId} className={styles.roomCard}>
            <div className={styles.roomImageContainer}>
              <Image
                src={`/rooms/${room.roomType.toLowerCase()}.jpg`}
                alt={room.roomType}
                layout="fill"
                objectFit="cover"
                className={styles.roomImage}
              />
              <div className={styles.roomStatus}>
                <span className={`${styles.statusBadge} ${styles[room.status.toLowerCase()]}`}>
                  {room.status}
                </span>
              </div>
            </div>

            <div className={styles.roomContent}>
              <h3 className={styles.roomName}>
                {room.roomType} - Room {room.roomNumber}
              </h3>
              
              <div className={styles.roomFeatures}>
                <span className={styles.feature}>Floor {room.floor}</span>
                <span className={styles.feature}>{room.bedType}</span>
                <span className={styles.feature}>{room.capacity} Guests</span>
              </div>

              <div className={styles.roomDescription}>
                <p>{room.roomTypeDescription}</p>
              </div>

              <div className={styles.amenities}>
                {room.amenities?.split(',').map((amenity, index) => (
                  <span key={index} className={styles.amenity}>{amenity.trim()}</span>
                ))}
              </div>

              <div className={styles.roomFooter}>
                <div className={styles.priceInfo}>
                  <span className={styles.priceLabel}>Per Night</span>
                  <span className={styles.priceValue}>${room.basePrice}</span>
                </div>
                {room.status === 'Available' && (
                  <button
                    className={styles.bookButton}
                    onClick={() => handleBooking(room)}
                  >
                    Đặt phòng 
                  </button>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>

      {selectedRoom && (
        <BookingModal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          room={{
            name: `${selectedRoom.roomType} - Room ${selectedRoom.roomNumber}`,
            price: `$${selectedRoom.basePrice}/night`,
            image: `/rooms/${selectedRoom.roomType.toLowerCase()}.jpg`
          }}
        />
      )}
    </div>
  );
};

export default RoomList;
