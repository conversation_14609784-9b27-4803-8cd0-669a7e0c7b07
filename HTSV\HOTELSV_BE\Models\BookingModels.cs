using System;

namespace HOTELSV_BE.Models
{
    public class BookingRequest
    {
        public int CustomerId { get; set; }
        public int EmployeeId { get; set; }
        public DateTime CheckInDate { get; set; }
        public DateTime CheckOutDate { get; set; }
        public int RoomTypeId { get; set; }
        public int Quantity { get; set; } = 1;
        public string Notes { get; set; }
        public string BookingSource { get; set; } = "Website";
    }

    public class BookingDetailResponse
    {
        public int BookingId { get; set; }
        public int CustomerId { get; set; }
        public int EmployeeId { get; set; }
        public DateTime BookingDate { get; set; }
        public DateTime CheckInDate { get; set; }
        public DateTime CheckOutDate { get; set; }
        public decimal TotalAmount { get; set; }
        public string BookingStatus { get; set; }
        public string PaymentStatus { get; set; }
        public string Notes { get; set; }
        public string BookingSource { get; set; }

        // Room Type details
        public int RoomTypeId { get; set; }
        public string Name { get; set; }
        public int RoomTypeQuantity { get; set; }
        public decimal RoomTypePrice { get; set; }

        // Room details
        public int? RoomId { get; set; }
        public string RoomNumber { get; set; }
        public DateTime? RoomCheckInDate { get; set; }
        public DateTime? RoomCheckOutDate { get; set; }
        public string RoomStatus { get; set; }
        public int? AssignedBy { get; set; }
    }
    public class UpdateBookingRequest
    {
        public int BookingId { get; set; }
        public int? CustomerId { get; set; }
        public int? EmployeeId { get; set; }
        public DateTime? CheckInDate { get; set; }
        public DateTime? CheckOutDate { get; set; }
        public decimal? TotalAmount { get; set; }
        public string Status { get; set; }
        public string PaymentStatus { get; set; }
        public string Notes { get; set; }
        public string BookingSource { get; set; }
    }

    public class UpdateBookingResponse
    {
        public int BookingId { get; set; }
        public int CustomerId { get; set; }
        public int EmployeeId { get; set; }
        public DateTime BookingDate { get; set; }
        public DateTime CheckInDate { get; set; }
        public DateTime CheckOutDate { get; set; }
        public decimal TotalAmount { get; set; }
        public string Status { get; set; }
        public string PaymentStatus { get; set; }
        public string Notes { get; set; }
        public string BookingSource { get; set; }
        public string CustomerName { get; set; }
        public string EmployeeName { get; set; }
    }
    public class UpdateBookingStatusRequest
    {
        public int BookingId { get; set; }
        public string Status { get; set; }
        public string PaymentStatus { get; set; }
    }

    public class BookingStatusResponse
    {
        public int BookingId { get; set; }
        public int CustomerId { get; set; }
        public int EmployeeId { get; set; }
        public DateTime BookingDate { get; set; }
        public DateTime CheckInDate { get; set; }
        public DateTime CheckOutDate { get; set; }
        public decimal TotalAmount { get; set; }
        public string Status { get; set; }
        public string PaymentStatus { get; set; }
        public string Notes { get; set; }
        public string BookingSource { get; set; }
        public string CustomerName { get; set; }
        public string EmployeeName { get; set; }
    }
    public class DeleteBookingResponse
    {
        public bool Success { get; set; }
        public string Message { get; set; }
    }

    public class BookingDetailsResponse
    {
        public int BookingId { get; set; }
        public DateTime BookingDate { get; set; }
        public DateTime CheckInDate { get; set; }
        public DateTime CheckOutDate { get; set; }
        public decimal TotalAmount { get; set; }
        public string BookingStatus { get; set; }
        public string PaymentStatus { get; set; }
        public string Notes { get; set; }
        public string BookingSource { get; set; }
        public string CustomerName { get; set; }
        public string CustomerPhone { get; set; }
        public string CustomerEmail { get; set; }
        public string EmployeeName { get; set; }
    }
}