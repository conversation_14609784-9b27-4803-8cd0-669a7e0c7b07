using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using System.Data.SqlClient;
using Dapper;
using System.Data;
using HOTELSV_BE.Models;
using HOTELSV_BE.Attributes;

namespace HOTELSV_BE.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class PaymentController : ControllerBase
    {
        private readonly string _connectionString;

        public PaymentController(IConfiguration configuration)
        {
            _connectionString = configuration.GetConnectionString("DefaultConnection") ??
                throw new ArgumentNullException(nameof(configuration), "DefaultConnection string is not configured");
        }

        [HttpGet("GetAll")]
        [RequirePermission("view_payments")]
        public async Task<ActionResult<IEnumerable<PaymentModels>>> GetAllPayments()
        {
            using (var connection = new SqlConnection(_connectionString))
            {
                var payments = await connection.QueryAsync<PaymentModels>(
                    "SELECT * FROM Payments"
                );
                return Ok(payments);
            }
        }

        [HttpGet("GetById/{id}")]
        [RequirePermission("view_payments")]
        public async Task<ActionResult<PaymentModels>> GetPaymentById(int id)
        {
            using (var connection = new SqlConnection(_connectionString))
            {
                var payment = await connection.QueryFirstOrDefaultAsync<PaymentModels>(
                    "SELECT * FROM Payments WHERE PaymentId = @Id",
                    new { Id = id }
                );

                if (payment == null)
                    return NotFound(new { message = "Không tìm thấy thanh toán với ID này." });

                return Ok(payment);
            }
        }

        [HttpPost("Add")]
        [RequirePermission("create_payment")]
        public async Task<ActionResult> AddPayment(AddPaymentModels payment)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    var parameters = new DynamicParameters();
                    parameters.Add("@InvoiceId", payment.InvoiceId);
                    parameters.Add("@Amount", payment.Amount);
                    parameters.Add("@PaymentMethod", payment.PaymentMethod);
                    parameters.Add("@TransactionId", payment.TransactionId);
                    parameters.Add("@ReceiptNumber", payment.ReceiptNumber);
                    parameters.Add("@ProcessedBy", payment.ProcessedBy);

                    var result = await connection.QueryFirstOrDefaultAsync<PaymentModels>(
                        "sp_AddPayment",
                        parameters,
                        commandType: CommandType.StoredProcedure
                    );

                    return Ok(new {
                        success = true,
                        message = "Thêm thanh toán thành công",
                        data = result
                    });
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new {
                    success = false,
                    message = "Lỗi khi thêm thanh toán",
                    error = ex.Message
                });
            }
        }

        [HttpPut("Update/{id}")]
        [RequirePermission("edit_payment")]
        public async Task<ActionResult> UpdatePayment(int id, UpdatePaymentModels payment)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    var parameters = new DynamicParameters();
                    parameters.Add("@PaymentId", id);
                    parameters.Add("@InvoiceId", payment.InvoiceId);
                    parameters.Add("@Amount", payment.Amount);
                    parameters.Add("@PaymentDate", payment.PaymentDate);
                    parameters.Add("@PaymentMethod", payment.PaymentMethod);
                    parameters.Add("@Status", payment.Status);
                    parameters.Add("@TransactionId", payment.TransactionId);
                    parameters.Add("@ReceiptNumber", payment.ReceiptNumber);
                    parameters.Add("@ProcessedBy", payment.ProcessedBy);

                    var result = await connection.ExecuteAsync(
                        "sp_UpdatePayment",
                        parameters,
                        commandType: CommandType.StoredProcedure
                    );

                    if (result == 0)
                    {
                        return NotFound(new {
                            success = false,
                            message = $"Không tìm thấy thanh toán với ID: {id}"
                        });
                    }

                    return Ok(new {
                        success = true,
                        message = "Cập nhật thanh toán thành công"
                    });
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new {
                    success = false,
                    message = "Lỗi khi cập nhật thanh toán",
                    error = ex.Message
                });
            }
        }

        [HttpDelete("Delete/{id}")]
        [RequirePermission("cancel_payment")]
        public async Task<IActionResult> DeletePayment(int id)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    var result = await connection.ExecuteAsync(
                        "DELETE FROM Payments WHERE PaymentId = @Id",
                        new { Id = id }
                    );

                    if (result == 0)
                    {
                        return NotFound(new {
                            success = false,
                            message = $"Không tìm thấy thanh toán với ID: {id}"
                        });
                    }

                    return Ok(new {
                        success = true,
                        message = "Xóa thanh toán thành công"
                    });
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new {
                    success = false,
                    message = "Lỗi khi xóa thanh toán",
                    error = ex.Message
                });
            }
        }
    }
}