import Head from 'next/head';
import Navbar from '../../components/Navbar';
import RoomList from '../../components/RoomList';
import styles from '../../styles/rooms.module.css';
import { useAuth } from '../../contexts/AuthContext';
import { useRouter } from 'next/router';
import { useEffect } from 'react';
import { toast } from 'react-toastify';

export default function RoomsPage() {
  const { isAuthenticated } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!isAuthenticated) {
      toast.error('Please login to view rooms');
      router.push('/auth/login');
    }
  }, [isAuthenticated, router]);

  if (!isAuthenticated) {
    return null;
  }

  return (
    <>
      <Head>
        <title>Rooms | Hotel Nhóm 1</title>
      </Head>
      <Navbar />
      <div className={styles.pageContainer}>
        <div className={styles.roomsHeader}>
          <h1><PERSON>h sách phòng</h1>
          <p><PERSON>h<PERSON>m phá không gian nghỉ dưỡng sang trọng</p>
        </div>
        <RoomList />
      </div>
    </>
  );
}