using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using System.Data.SqlClient;
using Dapper;
using System.Data;
using HOTELSV_BE.Models;
using HOTELSV_BE.Attributes;

namespace HOTELSV_BE.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class EmployeeController : ControllerBase
    {
        private readonly IConfiguration _configuration;
        private readonly string _connectionString;

        public EmployeeController(IConfiguration configuration)
        {
            _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
            _connectionString = _configuration.GetConnectionString("DefaultConnection") ?? throw new InvalidOperationException("Connection string 'DefaultConnection' not found.");
        }

        [HttpPost("Add")]
        [RequirePermission("create_employee")]
        public async Task<IActionResult> AddEmployee([FromBody] AddEmployeeRequest request)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    await connection.OpenAsync();

                    var parameters = new DynamicParameters();
                    parameters.Add("@Username", request.Username);
                    parameters.Add("@PasswordHash", request.PasswordHash);
                    parameters.Add("@Email", request.Email);
                    parameters.Add("@FirstName", request.FirstName);
                    parameters.Add("@LastName", request.LastName);
                    parameters.Add("@Phone", request.Phone);
                    parameters.Add("@Position", request.Position);
                    parameters.Add("@Department", request.Department);
                    parameters.Add("@Salary", request.Salary);
                    parameters.Add("@ManagerId", request.ManagerId);

                    var result = await connection.QueryFirstOrDefaultAsync<AddEmployeeResponse>(
                        "sp_AddEmployee",
                        parameters,
                        commandType: CommandType.StoredProcedure
                    );

                    if (result != null && result.IsSuccess)
                    {
                        return Ok(new
                        {
                            success = true,
                            message = "Employee added successfully",
                            data = result
                        });
                    }
                    else
                    {
                        return BadRequest(new
                        {
                            success = false,
                            message = result?.ErrorMessage ?? "Failed to add employee"
                        });
                    }
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    success = false,
                    message = "An error occurred while processing your request",
                    data = ex.Message
                });
            }
        }

        [HttpDelete("Delete/{id}")]
        [RequirePermission("cancel_employee")]
        public async Task<IActionResult> DeleteEmployee(int id)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    await connection.OpenAsync();

                    var parameters = new DynamicParameters();
                    parameters.Add("@EmployeeId", id);

                    var result = await connection.QueryFirstOrDefaultAsync<DeleteEmployeeResponse>(
                        "sp_DeleteEmployee",
                        parameters,
                        commandType: CommandType.StoredProcedure
                    );

                    if (result != null && result.IsSuccess)
                    {
                        return Ok(new
                        {
                            success = true,
                            message = result.Message ?? "Employee deleted successfully",
                            data = result
                        });
                    }
                    else
                    {
                        return BadRequest(new
                        {
                            success = false,
                            message = result?.ErrorMessage ?? "Failed to delete employee"
                        });
                    }
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    success = false,
                    message = "An error occurred while processing your request",
                    data = ex.Message
                });
            }
        }

        [HttpPut("Update/{id}")]
        [RequirePermission("edit_employees")]
        public async Task<IActionResult> UpdateEmployee(int id, [FromBody] UpdateEmployeeRequest request)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    await connection.OpenAsync();

                    var parameters = new DynamicParameters();
                    parameters.Add("@EmployeeId", id);
                    parameters.Add("@Position", request.Position);
                    parameters.Add("@Department", request.Department);
                    parameters.Add("@Salary", request.Salary);
                    parameters.Add("@ManagerId", request.ManagerId);
                    parameters.Add("@IsActive", request.IsActive);

                    var result = await connection.QueryFirstOrDefaultAsync<UpdateEmployeeResponse>(
                        "sp_UpdateEmployee",
                        parameters,
                        commandType: CommandType.StoredProcedure
                    );

                    if (result != null && result.IsSuccess)
                    {
                        return Ok(new
                        {
                            success = true,
                            message = "Employee updated successfully",
                            data = result
                        });
                    }
                    else
                    {
                        return BadRequest(new
                        {
                            success = false,
                            message = result?.ErrorMessage ?? "Failed to update employee"
                        });
                    }
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    success = false,
                    message = "An error occurred while processing your request",
                    data = ex.Message
                });
            }
        }

        [HttpGet("GetAll")]
        [RequirePermission("view_employees")]
        public async Task<IActionResult> GetAllEmployees()
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    await connection.OpenAsync();

                    var employees = await connection.QueryAsync<GetEmployeeResponse>(
                        "GetAllEmployees",
                        commandType: CommandType.StoredProcedure
                    );

                    return Ok(new
                    {
                        success = true,
                        message = "Retrieved employees successfully",
                        data = employees
                    });
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    success = false,
                    message = "An error occurred while retrieving employees",
                    data = ex.Message
                });
            }
        }

        [HttpGet("GetById/{id}")]
        [RequirePermission("view_employees")]
        public async Task<IActionResult> GetEmployeeById(int id)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    await connection.OpenAsync();

                    var parameters = new DynamicParameters();
                    parameters.Add("@EmployeeId", id);

                    var employee = await connection.QueryFirstOrDefaultAsync<GetEmployeeResponse>(
                        "GetEmployeeById",
                        parameters,
                        commandType: CommandType.StoredProcedure
                    );

                    if (employee == null)
                    {
                        return NotFound(new
                        {
                            success = false,
                            message = $"Employee not found with ID: {id}"
                        });
                    }

                    return Ok(new
                    {
                        success = true,
                        message = "Retrieved employee successfully",
                        data = employee
                    });
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    success = false,
                    message = "An error occurred while retrieving employee",
                    data = ex.Message
                });
            }
        }
    }
}
