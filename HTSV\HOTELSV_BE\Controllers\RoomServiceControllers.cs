using Dapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Data.SqlClient;
using System.Data;
using HOTELSV_BE.Attributes;

namespace HOTELSV_BE.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class RoomServicesController : ControllerBase
    {
        private readonly string _connectionString;

        public RoomServicesController(IConfiguration configuration)
        {
            _connectionString = configuration.GetConnectionString("DefaultConnection");
        }

        [HttpGet("GetAllRoomServices")]
        [RequirePermission("view_services")]
        public async Task<IActionResult> GetAllRoomServices()
        {
            using (var connection = new SqlConnection(_connectionString))
            {
                var roomServices = await connection.QueryAsync<RoomService>("SELECT * FROM RoomServices");
                return Ok(roomServices);
            }
        }

        [HttpGet("GetRoomServicesBy/{id}")]
        [RequirePermission("view_services")]
        public async Task<IActionResult> GetRoomServicesById(int id)
        {
            using (var connection = new SqlConnection(_connectionString))
            {
                var roomServices = await connection.QueryFirstOrDefaultAsync<RoomService>(
                    "SELECT * FROM RoomTypes WHERE ServiceId = @Id",
                    new { Id = id });

                if (roomServices == null)
                    return NotFound(new { message = "Không tìm thấy loại dịch vụ khớp với Id này." });

                return Ok(roomServices);
            }
        }

        [HttpPost("AddRoomService")]
        [RequirePermission("manage_services")]
        public async Task<ActionResult> AddRoomService(AddRoomService addRoomservice)
        {
            try {
                using (var connection = new SqlConnection(_connectionString))
                {
                    var parameters = new DynamicParameters();
                    parameters.Add("@ServiceName", addRoomservice.ServiceName);
                    parameters.Add("@Description", addRoomservice.Description);
                    parameters.Add("@Price", addRoomservice.Price);
                    parameters.Add("@IsActive", addRoomservice.IsActive);

                    var result = await connection.QueryFirstOrDefaultAsync<RoomType>(
                        "sp_AddService",
                        parameters,
                        commandType: CommandType.StoredProcedure);

                    return Ok(result);
                }
            } catch (Exception ex) {
                return BadRequest(new { message = "Có lỗi xảy ra khi thêm dịch vụ phòng.", error = ex.Message });
            }
        }

        [HttpPut("Update/{id}")]
        [RequirePermission("manage_services")]
        public async Task<IActionResult> Update(int id, AddRoomService updateroomService)
        {
            using (var connection = new SqlConnection(_connectionString))
            {
                var parameters = new DynamicParameters();
                parameters.Add("@ServiceId", id);
                parameters.Add("@ServiceName", updateroomService.ServiceName);
                parameters.Add("@Description", updateroomService.Description);
                parameters.Add("@Price", updateroomService.Price);
                parameters.Add("@IsActive", updateroomService.IsActive);

                var result = await connection.QueryFirstOrDefaultAsync<RoomType>(
                    "sp_UpdateRoomService",
                    parameters,
                    commandType: CommandType.StoredProcedure);

                if (result == null)
                    return NotFound(new { message = "Cập nhật dịch vụ phòng không thành công" });

                return Ok(result);
            }
        }

        [HttpDelete("DeleteRoomServiceBy/{id}")]
        [RequirePermission("manage_services")]
        public async Task<IActionResult> DeleteRoomService(int id)
        {
            using (var connection = new SqlConnection(_connectionString))
            {
                var result = await connection.ExecuteAsync(
                    "DELETE FROM RoomServices WHERE ServiceId = @Id",
                    new { Id = id });

                if (result == 0)
                    return NotFound(new { message = "Xóa dịch vụ phòng không thành công" });
                    
                return Ok(new { message = "Xóa dịch vụ phòng thành công" });
            }
        }
    }
}