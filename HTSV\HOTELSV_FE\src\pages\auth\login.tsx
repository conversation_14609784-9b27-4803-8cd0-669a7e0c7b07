import { useState } from "react";
import Link from "next/link";
import Navbar from '../../components/Navbar';
import styles from "../../styles/auth.module.css";
import { useAuth } from '../../contexts/AuthContext';
import { useRouter } from 'next/router';
import { toast } from 'react-toastify';
import { authService } from '../../services/authService';

export default function Login() {
  const { login } = useAuth();
  const router = useRouter();

  const [formData, setFormData] = useState({
    email: '',
    userName: '',
    password: ''
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      if (!formData.email && !formData.userName) {
        toast.error('<PERSON><PERSON> lòng nhập email hoặc tên đăng nhập');
        return;
      }

      if (!formData.password) {
        toast.error('Vui lòng nhập mật khẩu');
        return;
      }

      const response = await authService.login(formData);
      localStorage.setItem('token', response.token);
      login(response);
      toast.success('Đăng nhập thành công!');
      router.push('/');
    } catch (error: any) {
      toast.error(error.message || 'Đăng nhập thất bại. Vui lòng thử lại!');
    }
  };

  return (
    <>
      <Navbar />
      <div className={styles.authContainer}>
        <div className={styles.authCard}>
          <h2 className={styles.authTitle}>Đăng nhập</h2>
          <form className={styles.authForm} onSubmit={handleSubmit}>
            <div className={styles.formGroup}>
              <label htmlFor="userName">Tên đăng nhập</label>
              <input
                type="text"
                id="userName"
                name="userName"
                value={formData.userName}
                onChange={handleChange}
                placeholder="Nhập tên đăng nhập"
              />
            </div>
            <div className={styles.formGroup}>
              <label htmlFor="email">Email</label>
              <input
                type="email"
                id="email"
                name="email"
                value={formData.email}
                onChange={handleChange}
                placeholder="Nhập email"
              />
            </div>
            <div className={styles.formGroup}>
              <label htmlFor="password">Mật khẩu</label>
              <input
                type="password"
                id="password"
                name="password"
                value={formData.password}
                onChange={handleChange}
                placeholder="Nhập mật khẩu"
                required
              />
            </div>
            <button type="submit" className={styles.authButton}>
              Đăng nhập
            </button>
          </form>
          <p className={styles.authSwitch}>
            Chưa có tài khoản?{" "}
            <Link href="/auth/register" className={styles.authLink}>
              Đăng ký ngay
            </Link>
          </p>
        </div>
      </div>
    </>
  );
}